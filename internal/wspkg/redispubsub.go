package wspkg

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"

	"github.com/redis/go-redis/v9"
)

// Message represents the structure of messages we'll publish
// type Message struct {
// 	ID        string    `json:"id"`
// 	Type      string    `json:"type"`
// 	Payload   string    `json:"payload"`
// 	Timestamp time.Time `json:"timestamp"`
// }

// PubSubManager handles Redis pub/sub operations
type PubSubManager struct {
	client      *redis.Client
	ctx         context.Context
	subscribers map[string]*redis.PubSub
	mu          sync.RWMutex
}

// NewPubSubManager creates a new pub/sub manager
func NewPubSubManager(redisURL string) (*PubSubManager, error) {
	opts, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Redis URL: %w", err)
	}

	client := redis.NewClient(opts)

	// Test connection
	ctx := context.Background()
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &PubSubManager{
		client:      client,
		ctx:         ctx,
		subscribers: make(map[string]*redis.PubSub),
	}, nil
}

// Publish publishes a message to a channel
func (pm *PubSubManager) Publish(channel string, msg Message) error {
	msgBytes, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	err = pm.client.Publish(pm.ctx, channel, msgBytes).Err()
	if err != nil {
		return fmt.Errorf("failed to publish message: %w", err)
	}

	return nil
}

// Subscribe subscribes to one or more channels
func (pm *PubSubManager) Subscribe(channels []string, handler func(channel string, msg *Message)) error {
	pubsub := pm.client.Subscribe(pm.ctx, channels...)

	// Store subscriber for cleanup
	key := fmt.Sprintf("%v", channels)
	pm.mu.Lock()
	pm.subscribers[key] = pubsub
	pm.mu.Unlock()

	// Wait for subscription confirmation
	_, err := pubsub.Receive(pm.ctx)
	if err != nil {
		return fmt.Errorf("failed to confirm subscription: %w", err)
	}

	log.Printf("Subscribed to channels: %v", channels)

	// Handle messages in a goroutine
	go func() {
		defer func() {
			pm.mu.Lock()
			delete(pm.subscribers, key)
			pm.mu.Unlock()
			pubsub.Close()
		}()

		ch := pubsub.Channel()
		for msg := range ch {
			var parsedMsg Message
			if err := json.Unmarshal([]byte(msg.Payload), &parsedMsg); err != nil {
				log.Printf("Failed to unmarshal message: %v", err)
				continue
			}

			handler(msg.Channel, &parsedMsg)
		}
	}()

	return nil
}

// SubscribePattern subscribes to channels matching a pattern
func (pm *PubSubManager) SubscribePattern(pattern string, handler func(channel string, msg Message)) error {
	pubsub := pm.client.PSubscribe(pm.ctx, pattern)

	// Store subscriber for cleanup
	pm.mu.Lock()
	pm.subscribers[pattern] = pubsub
	pm.mu.Unlock()

	// Wait for subscription confirmation
	_, err := pubsub.Receive(pm.ctx)
	if err != nil {
		return fmt.Errorf("failed to confirm pattern subscription: %w", err)
	}

	log.Printf("Subscribed to pattern: %s", pattern)

	// Handle messages in a goroutine
	go func() {
		defer func() {
			pm.mu.Lock()
			delete(pm.subscribers, pattern)
			pm.mu.Unlock()
			pubsub.Close()
		}()

		ch := pubsub.Channel()
		for msg := range ch {
			var parsedMsg Message
			if err := json.Unmarshal([]byte(msg.Payload), &parsedMsg); err != nil {
				log.Printf("Failed to unmarshal message: %v", err)
				continue
			}

			handler(msg.Channel, parsedMsg)
		}
	}()

	return nil
}

// Close closes all subscriptions and the Redis client
func (pm *PubSubManager) Close() error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	// Close all subscribers
	for _, sub := range pm.subscribers {
		sub.Close()
	}

	// Close Redis client
	return pm.client.Close()
}

// GetStats returns basic statistics about active subscriptions
func (pm *PubSubManager) GetStats() map[string]interface{} {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	return map[string]interface{}{
		"active_subscriptions": len(pm.subscribers),
		"redis_pool_stats":     pm.client.PoolStats(),
	}
}
