package wspkg

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

type Client struct {
	Id            string
	conn          *websocket.Conn
	send          chan *Message
	online        bool
	authenticated bool
	errHandler    func(error)
	closeHandler  func(c *Client) error
	ctx           context.Context
	cancel        context.CancelFunc
	mut           sync.RWMutex
	closeOnce     sync.Once
	closed        int32 // atomic flag to prevent double cleanup
}

func (c *Client) Close() {
	// Use atomic flag to prevent double cleanup
	if !atomic.CompareAndSwapInt32(&c.closed, 0, 1) {
		return // Already closed
	}

	// Cancel context first to signal all goroutines
	if c.cancel != nil {
		c.cancel()
	}

	c.mut.Lock()
	defer c.mut.Unlock()

	// Close connection
	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	c.online = false

	// Close send channel if not already closed
	c.closeOnce.Do(func() {
		close(c.send)
	})

	// Call close handler if set
	if c.closeHandler != nil {
		// Use a goroutine to prevent blocking and potential deadlocks
		go func() {
			defer func() {
				if r := recover(); r != nil {
					if c.errHandler != nil {
						c.errHandler(fmt.Errorf("panic in close handler: %v", r))
					}
				}
			}()
			c.closeHandler(c)
		}()
	}
}

func (c *Client) SetOnline(b bool) {
	c.mut.Lock()
	defer c.mut.Unlock()
	c.online = b
}

func (c *Client) IsOnline() bool {
	c.mut.RLock()
	defer c.mut.RUnlock()
	return c.online
}

// newClient creates a new Client instance with the provided connection
// If id is empty, a new UUID will be generated
func NewClient(conn *websocket.Conn) *Client {
	id := uuid.New().String()

	ctx, cancel := context.WithCancel(context.Background())

	return &Client{
		Id:     id,
		conn:   conn,
		send:   make(chan *Message, 256), // buffered channel
		online: true,
		ctx:    ctx,
		cancel: cancel,
		mut:    sync.RWMutex{},
	}
}

func (c *Client) SetErrHandler(handler func(error)) {
	c.errHandler = handler
}

func (c *Client) SetCloseHandler(handler func(c *Client) error) {
	c.closeHandler = handler
}

func (c *Client) Send(msg *Message) {
	// Check if client is closed atomically
	if atomic.LoadInt32(&c.closed) == 1 {
		return
	}

	if !c.IsOnline() {
		return
	}

	select {
	case c.send <- msg:
		// Message sent successfully
	case <-c.ctx.Done():
		// Context cancelled, client is being closed
		return
	default:
		// Channel is full, handle gracefully
		if c.errHandler != nil {
			c.errHandler(fmt.Errorf("send channel is full for client %s", c.Id))
		}
		// Optionally close the client if channel is consistently full
		go c.Close()
	}
}

// IsConnected checks if the client connection is still valid
func (c *Client) IsConnected() bool {
	// Check atomic flag first
	if atomic.LoadInt32(&c.closed) == 1 {
		return false
	}

	c.mut.RLock()
	defer c.mut.RUnlock()
	return c.conn != nil && c.online
}

// IsClosed returns true if the client has been closed
func (c *Client) IsClosed() bool {
	return atomic.LoadInt32(&c.closed) == 1
}
